const { QueryTypes } = require("sequelize");
const dbConfig = require("./shared/config/db.json");
const { Sequelize } = require("sequelize");

const ENV = "production";
const config = dbConfig[ENV];

const sequelize = new Sequelize(config.database, config.username, config.password, {
  host: config.host,
  port: config.port,
  dialect: config.dialect,
  logging: false,
});

async function completeProductionSetup() {
  try {
    console.log("🚀 COMPLETE PRODUCTION SETUP & VERIFICATION");
    console.log("===========================================");
    console.log(`Environment: ${ENV}`);
    console.log(`Database: ${config.database}`);
    console.log(`Host: ${config.host}`);
    console.log("");

    // Step 1: Verify all tables exist
    console.log("📋 Step 1: Verifying Database Tables...");
    const requiredTables = [
      'mo_category',
      'mo_food_attributes', 
      'mo_cuisine_attributes',
      'mo_recipe_measure',
      'mo_nutrition_attributes',
      'mo_haccp_attributes',
      'mo_cooking_preparation_methods',
      'nv_items'
    ];

    for (const table of requiredTables) {
      const exists = await sequelize.query(
        `SHOW TABLES LIKE '${table}'`,
        { type: QueryTypes.SELECT }
      );
      console.log(`   ${table}: ${exists.length > 0 ? '✅ EXISTS' : '❌ MISSING'}`);
    }

    // Step 2: Check Recipe Categories
    console.log("\n📁 Step 2: Recipe Categories Status...");
    const recipeCategories = await sequelize.query(
      `SELECT category_name, category_icon FROM mo_category 
       WHERE category_type = 'recipe' AND organization_id IS NULL 
       ORDER BY category_name`,
      { type: QueryTypes.SELECT }
    );

    console.log(`   Total Recipe Categories: ${recipeCategories.length}`);
    const recipeCategoriesWithIcons = recipeCategories.filter(c => c.category_icon).length;
    console.log(`   Categories with Icons: ${recipeCategoriesWithIcons}/${recipeCategories.length}`);

    if (recipeCategories.length > 0) {
      console.log("   Categories:");
      recipeCategories.forEach(cat => {
        console.log(`     ${cat.category_name}: ${cat.category_icon ? `✅ ${cat.category_icon}` : '❌ NO ICON'}`);
      });
    }

    // Step 3: Check Ingredient Categories  
    console.log("\n🥕 Step 3: Ingredient Categories Status...");
    const ingredientCategories = await sequelize.query(
      `SELECT category_name, category_icon FROM mo_category 
       WHERE category_type = 'ingredient' AND organization_id IS NULL 
       ORDER BY category_name`,
      { type: QueryTypes.SELECT }
    );

    console.log(`   Total Ingredient Categories: ${ingredientCategories.length}`);
    const ingredientCategoriesWithIcons = ingredientCategories.filter(c => c.category_icon).length;
    console.log(`   Categories with Icons: ${ingredientCategoriesWithIcons}/${ingredientCategories.length}`);

    if (ingredientCategories.length > 0) {
      console.log("   Categories:");
      ingredientCategories.forEach(cat => {
        console.log(`     ${cat.category_name}: ${cat.category_icon ? `✅ ${cat.category_icon}` : '❌ NO ICON'}`);
      });
    }

    // Step 4: Check Food Attributes (Allergens)
    console.log("\n🚨 Step 4: Food Attributes (Allergens) Status...");
    const allergens = await sequelize.query(
      `SELECT attribute_title, attribute_icon FROM mo_food_attributes 
       WHERE attribute_type = 'allergen' AND organization_id IS NULL 
       ORDER BY attribute_title`,
      { type: QueryTypes.SELECT }
    );

    console.log(`   Total Allergens: ${allergens.length}`);
    const allergensWithIcons = allergens.filter(a => a.attribute_icon).length;
    console.log(`   Allergens with Icons: ${allergensWithIcons}/${allergens.length}`);

    if (allergens.length > 0) {
      console.log("   Allergens:");
      allergens.forEach(attr => {
        console.log(`     ${attr.attribute_title}: ${attr.attribute_icon ? `✅ ${attr.attribute_icon}` : '❌ NO ICON'}`);
      });
    }

    // Step 5: Check Other Attributes
    console.log("\n🌍 Step 5: Other System Attributes...");
    
    const cuisineCount = await sequelize.query(
      "SELECT COUNT(*) as count FROM mo_cuisine_attributes WHERE organization_id IS NULL",
      { type: QueryTypes.SELECT }
    );
    console.log(`   Cuisine Attributes: ${cuisineCount[0].count}`);

    const measureCount = await sequelize.query(
      "SELECT COUNT(*) as count FROM mo_recipe_measure WHERE organization_id IS NULL",
      { type: QueryTypes.SELECT }
    );
    console.log(`   Recipe Measures: ${measureCount[0].count}`);

    const nutritionCount = await sequelize.query(
      "SELECT COUNT(*) as count FROM mo_nutrition_attributes WHERE organization_id IS NULL",
      { type: QueryTypes.SELECT }
    );
    console.log(`   Nutrition Attributes: ${nutritionCount[0].count}`);

    const haccpCount = await sequelize.query(
      "SELECT COUNT(*) as count FROM mo_haccp_attributes WHERE organization_id IS NULL",
      { type: QueryTypes.SELECT }
    );
    console.log(`   HACCP Attributes: ${haccpCount[0].count}`);

    const cookingCount = await sequelize.query(
      "SELECT COUNT(*) as count FROM mo_cooking_preparation_methods WHERE organization_id IS NULL",
      { type: QueryTypes.SELECT }
    );
    console.log(`   Cooking Methods: ${cookingCount[0].count}`);

    // Step 6: Check Icon Items
    console.log("\n📦 Step 6: Icon Items Status...");
    const iconItems = await sequelize.query(
      `SELECT COUNT(*) as count FROM nv_items 
       WHERE item_category IN ('category_icon', 'attribute_icon') 
       AND item_organization_id IS NULL`,
      { type: QueryTypes.SELECT }
    );
    console.log(`   Total Icon Items: ${iconItems[0].count}`);

    // Step 7: Final Assessment
    console.log("\n📊 FINAL ASSESSMENT");
    console.log("===================");
    
    const expectedRecipeCategories = 11;
    const expectedIngredientCategories = 14;
    const expectedAllergens = 14;
    const expectedIconItems = 39;

    const allDataComplete = 
      recipeCategories.length >= expectedRecipeCategories &&
      ingredientCategories.length >= expectedIngredientCategories &&
      allergens.length >= expectedAllergens &&
      cuisineCount[0].count > 0 &&
      measureCount[0].count > 0 &&
      nutritionCount[0].count > 0 &&
      haccpCount[0].count > 0 &&
      cookingCount[0].count > 0;

    const allIconsComplete = 
      recipeCategoriesWithIcons >= expectedRecipeCategories &&
      ingredientCategoriesWithIcons >= expectedIngredientCategories &&
      allergensWithIcons >= expectedAllergens &&
      iconItems[0].count >= expectedIconItems;

    console.log(`✅ Default Data Complete: ${allDataComplete ? 'YES' : 'NO'}`);
    console.log(`✅ Icons Complete: ${allIconsComplete ? 'YES' : 'NO'}`);
    console.log(`✅ Production Ready: ${allDataComplete && allIconsComplete ? 'YES' : 'NO'}`);

    if (!allDataComplete) {
      console.log("\n⚠️  MISSING DEFAULT DATA - Run: npm run db:seed:prod");
    }

    if (!allIconsComplete) {
      console.log("\n⚠️  MISSING ICONS - Run: npm run icons:seed:force:prod");
    }

    if (allDataComplete && allIconsComplete) {
      console.log("\n🎉 PRODUCTION SETUP COMPLETE!");
      console.log("   All default data and icons are properly seeded.");
      console.log("   Your production environment is ready to use!");
    }

  } catch (error) {
    console.error("❌ Error during verification:", error.message);
  } finally {
    await sequelize.close();
  }
}

completeProductionSetup();
