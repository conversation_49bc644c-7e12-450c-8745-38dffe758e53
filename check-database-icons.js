const { QueryTypes } = require("sequelize");
const path = require("path");

// Import database configuration
const dbConfig = require("./shared/config/db.json");
const { Sequelize } = require("sequelize");

const ENV = process.env.NODE_ENV || "production";
const config = dbConfig[ENV];

// Create Sequelize instance
const sequelize = new Sequelize(config.database, config.username, config.password, {
  host: config.host,
  port: config.port,
  dialect: config.dialect,
  logging: false,
});

async function checkDatabaseIcons() {
  try {
    console.log("🔍 Checking database icon status...");
    console.log(`Environment: ${ENV}`);
    console.log("");

    // Check recipe categories
    console.log("📁 Recipe Categories:");
    const recipeCategories = await sequelize.query(
      `SELECT category_name, category_icon FROM mo_category 
       WHERE category_type = 'recipe' AND organization_id IS NULL 
       ORDER BY category_name`,
      { type: QueryTypes.SELECT }
    );

    recipeCategories.forEach(cat => {
      console.log(`   ${cat.category_name}: ${cat.category_icon ? `✅ ${cat.category_icon}` : '❌ NULL'}`);
    });

    console.log("");

    // Check ingredient categories
    console.log("🥕 Ingredient Categories:");
    const ingredientCategories = await sequelize.query(
      `SELECT category_name, category_icon FROM mo_category 
       WHERE category_type = 'ingredient' AND organization_id IS NULL 
       ORDER BY category_name`,
      { type: QueryTypes.SELECT }
    );

    ingredientCategories.forEach(cat => {
      console.log(`   ${cat.category_name}: ${cat.category_icon ? `✅ ${cat.category_icon}` : '❌ NULL'}`);
    });

    console.log("");

    // Check food attributes (allergens)
    console.log("🚨 Food Attributes (Allergens):");
    const allergens = await sequelize.query(
      `SELECT attribute_title, attribute_icon FROM mo_food_attributes 
       WHERE attribute_type = 'allergen' AND organization_id IS NULL 
       ORDER BY attribute_title`,
      { type: QueryTypes.SELECT }
    );

    allergens.forEach(attr => {
      console.log(`   ${attr.attribute_title}: ${attr.attribute_icon ? `✅ ${attr.attribute_icon}` : '❌ NULL'}`);
    });

    console.log("");

    // Check nv_items for uploaded icons
    console.log("📦 Icon Items in nv_items:");
    const iconItems = await sequelize.query(
      `SELECT id, item_name, item_category FROM nv_items
       WHERE item_category IN ('category_icon', 'attribute_icon')
       AND item_organization_id IS NULL
       ORDER BY id DESC LIMIT 10`,
      { type: QueryTypes.SELECT }
    );

    iconItems.forEach(item => {
      console.log(`   ID: ${item.id} | ${item.item_category} | ${item.item_name}`);
    });

    console.log("");

    // Summary
    const recipeWithIcons = recipeCategories.filter(c => c.category_icon).length;
    const ingredientWithIcons = ingredientCategories.filter(c => c.category_icon).length;
    const allergensWithIcons = allergens.filter(a => a.attribute_icon).length;

    console.log("📊 Summary:");
    console.log(`   Recipe Categories with icons: ${recipeWithIcons}/${recipeCategories.length}`);
    console.log(`   Ingredient Categories with icons: ${ingredientWithIcons}/${ingredientCategories.length}`);
    console.log(`   Allergens with icons: ${allergensWithIcons}/${allergens.length}`);
    console.log(`   Total icon items in nv_items: ${iconItems.length}`);

    if (recipeWithIcons === 0 && ingredientWithIcons === 0 && allergensWithIcons === 0) {
      console.log("");
      console.log("❌ PROBLEM IDENTIFIED: Icons uploaded to S3 but database not updated!");
      console.log("💡 Solution: Run force update to link existing icons to categories/attributes");
    }

  } catch (error) {
    console.error("❌ Error checking database:", error.message);
  } finally {
    await sequelize.close();
  }
}

checkDatabaseIcons();
